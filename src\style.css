@tailwind base;
@tailwind components;
@tailwind utilities;

:root {
  --chat-font-size: 16px;
  --chat-padding: 16px;
}

/* Custom animations */
@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes slideIn {
  from {
    opacity: 0;
    transform: translateX(-20px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

.animate-fade-in {
  animation: fadeIn 0.3s ease-out;
}

.animate-slide-in {
  animation: slideIn 0.3s ease-out;
}

/* Smooth scrolling for chat container */
.overflow-y-auto {
  scroll-behavior: smooth;
}

/* Force remove border from chatbot container */
.fixed.bottom-6.right-6.z-50 > div {
  border: none !important;
}

/* Responsive classes */
.responsive-text {
  font-size: var(--chat-font-size);
}

.responsive-padding {
  padding: var(--chat-padding);
}

/* Hide non-essential elements on small screens */
@media (max-width: 375px) {
  .non-essential {
    display: none;
  }
}
