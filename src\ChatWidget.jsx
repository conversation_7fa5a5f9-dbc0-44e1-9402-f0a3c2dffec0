import { useState, useEffect, useRef } from 'react';
import { saveLead } from './lib/supabaseClient';
import ChatHeader from './components/ChatHeader';
import MessageList from './components/MessageList';
import LeadForm from './components/LeadForm';
import ChatInput from './components/ChatInput';
import MinimizedButton from './components/MinimizedButton';
import { StagewiseToolbar } from '@stagewise/toolbar-react';
import { ReactPlugin } from '@stagewise-plugins/react';

export default function ChatWidget() {
  const [isOpen, setIsOpen] = useState(false);
  const [messages, setMessages] = useState([
    { text: "Hi there! 👋 I'm the UpZera assistant. We build smart digital tools that move businesses forward. Ask me about our web development, AI chatbots, or book a free consultation!", isUser: false }
  ]);
  const [inputValue, setInputValue] = useState('');
  const [companyKnowledge, setCompanyKnowledge] = useState({});
  const [isBotTyping, setIsBotTyping] = useState(false);
  const [leadForm, setLeadForm] = useState({
    name: '',
    email: '',
    isSubmitting: false,
    error: null
  });
  const [showLeadForm, setShowLeadForm] = useState(false);
  const [showCalendly, setShowCalendly] = useState(false);
  const [isConversationEnded, setIsConversationEnded] = useState(false);
  const [screenSize, setScreenSize] = useState({
    width: typeof window !== 'undefined' ? window.innerWidth : 1200,
    height: typeof window !== 'undefined' ? window.innerHeight : 800,
    isSmall: false,
  });
  const chatContainerRef = useRef(null);

  const scrollToBottom = () => {
    if (chatContainerRef.current) {
      setTimeout(() => {
        chatContainerRef.current.scrollTop = chatContainerRef.current.scrollHeight;
      }, 100);
    }
  };

  // Set up CSS variables based on screen size
  useEffect(() => {
    const root = document.documentElement;
    if (screenSize.isSmall) {
      // Mobile-friendly sizes - different variations based on actual width
      if (screenSize.width <= 320) { // iPhone SE, smaller devices
        root.style.setProperty('--chat-font-size', '13px');
        root.style.setProperty('--chat-padding', '6px');
      } else if (screenSize.width <= 375) { // iPhone X/11/12 mini
        root.style.setProperty('--chat-font-size', '14px');
        root.style.setProperty('--chat-padding', '8px');
      } else { // Other mobile devices
        root.style.setProperty('--chat-font-size', '15px');
        root.style.setProperty('--chat-padding', '10px');
      }
    } else {
      // Reset to default sizes
      root.style.setProperty('--chat-font-size', '16px');
      root.style.setProperty('--chat-padding', '16px');
    }
  }, [screenSize]);

  // Listen for screen size changes internally too
  useEffect(() => {
    if (typeof window === 'undefined') return;

    const handleResize = () => {
      const width = window.innerWidth;
      const height = window.innerHeight;
      const isSmall = width <= 768; // Consider tablets and phones as "small"
      
      setScreenSize({ width, height, isSmall });
    };

    // Set initial size
    handleResize();
    
    // Add resize listener
    window.addEventListener('resize', handleResize);
    
    return () => {
      window.removeEventListener('resize', handleResize);
    };
  }, []);

  useEffect(() => {
    const allowedOrigins = ['https://upzera-web.netlify.app', 'http://localhost:3000', 'https://chatbot-test123.netlify.app'];
    const handleMessage = (event) => {
      if (!allowedOrigins.includes(event.origin)) return;
      
      if (event.data.type === 'toggleChat') {
        setIsOpen(event.data.payload.isOpen);
      }
      else if (event.data.type === 'closeChatbot') {
        setIsOpen(false);
      }
      else if (event.data.type === 'responsiveView') {
        // Handle responsive view message from parent
        const { isSmallScreen, width, height } = event.data.payload;
        
        // Update our screen size state based on parent information
        setScreenSize({
          width: width || window.innerWidth,
          height: height || window.innerHeight,
          isSmall: isSmallScreen
        });
        
        console.log(`Received screen info from parent: ${width}x${height}, isSmall: ${isSmallScreen}`);
      }
    };
    
    window.addEventListener('message', handleMessage);
    return () => window.removeEventListener('message', handleMessage);
  }, []);

  useEffect(() => {
    scrollToBottom();
  }, [messages, showLeadForm]);

  useEffect(() => {
    fetch('/company_knowledge.json')
      .then(res => {
        if (!res.ok) throw new Error(`HTTP error! status: ${res.status}`);
        return res.json();
      })
      .then(data => setCompanyKnowledge(data))
      .catch(err => {
        console.error('Error loading knowledge base:', err);
        alert('Failed to load company knowledge. Please check console for details.');
      });
  }, []);

  const findKnowledgeMatch = (question) => {
    const lower = question.toLowerCase();

    // Don't use knowledge base for human contact requests
    if (/speak|talk|connect|chat|contact|representative|person|human|agent|real/i.test(lower)) return null;

    // Enhanced keyword matching for FAQs with better scoring
    let bestMatch = null;
    let bestScore = 0;

    for (const faq of companyKnowledge.faqs || []) {
      let score = 0;
      for (const keyword of faq.keywords) {
        const keywordLower = keyword.toLowerCase();
        if (lower.includes(keywordLower)) {
          // Give higher score for exact matches and longer keywords
          score += keyword.length > 3 ? 2 : 1;
        }
        // Also check for partial matches at word boundaries
        const words = lower.split(/\s+/);
        for (const word of words) {
          if (word === keywordLower) {
            score += 3; // Exact word match gets highest score
          } else if (word.includes(keywordLower) || keywordLower.includes(word)) {
            score += 1; // Partial word match gets some score
          }
        }
      }
      if (score > bestScore) {
        bestScore = score;
        bestMatch = faq.answer;
      }
    }

    // If we found a good FAQ match, return it (lowered threshold)
    if (bestScore >= 1) return bestMatch;

    // Enhanced service matching with detailed responses
    for (const service of companyKnowledge.services || []) {
      let score = 0;
      for (const keyword of service.keywords) {
        if (lower.includes(keyword.toLowerCase())) {
          score += keyword.length > 3 ? 2 : 1;
        }
      }
      if (score >= 2) {
        // Return more detailed service info
        const priceInfo = service.service_tiers?.[0]?.price_range ?
          ` Starting from ${service.service_tiers[0].price_range}.` : '';
        return `${service.name}: ${service.description}${priceInfo}`;
      }
    }

    // Company info queries - more flexible founding date patterns
    if (/when.*(upzera|company).*(founded|established|created|started)|founding.*(year|date)|(upzera|company).*(founded|established|created|started)/i.test(lower)) {
      return `UpZera was founded in ${companyKnowledge.company_info?.founding_year || 2025}. We're based in ${companyKnowledge.company_info?.location || 'Eindhoven, Netherlands'}.`;
    }

    if (/where|location|address|based/i.test(lower)) {
      return `We're located at ${companyKnowledge.company_info?.address || 'Eindhoven, Netherlands'}. Feel free to reach out anytime!`;
    }

    if (/mission|purpose|goal/i.test(lower)) {
      return companyKnowledge.company_info?.mission || 'We build smart digital tools that actually move businesses forward.';
    }

    // Greetings with context
    if (/hello|hi|hey|good morning|good afternoon|good evening/i.test(lower)) {
      const greetings = companyKnowledge.conversation_flows?.greetings || ['Hello! How can I help you today?'];
      return greetings[Math.floor(Math.random() * greetings.length)];
    }

    return null;
  };

  const getAIResponse = async (userInput) => {
    try {
      // Get more conversation context (last 6 messages instead of 3) but optimize for tokens
      const recentMessages = messages.slice(-6).map(m =>
        m.isUser ? `User: ${m.text}` : `Assistant: ${m.text}`
      ).join('\n');

      // Create comprehensive but token-optimized knowledge context
      const knowledgeContext = {
        company: {
          name: companyKnowledge.company_info?.name,
          mission: companyKnowledge.company_info?.mission,
          location: companyKnowledge.company_info?.location,
          tagline: companyKnowledge.company_info?.tagline,
          values: companyKnowledge.company_info?.values?.slice(0, 4), // Limit to key values
        },
        services: companyKnowledge.services?.map(s => ({
          name: s.name,
          description: s.description,
          price: s.service_tiers?.[0]?.price_range || 'Contact for pricing'
        })) || [],
        contact: {
          email: companyKnowledge.contact_info?.email,
          phone: companyKnowledge.contact_info?.phone,
          calendly: companyKnowledge.contact_info?.calendly_url
        },
        faqs: companyKnowledge.faqs?.slice(0, 8).map(f => ({ // Limit to top 8 FAQs
          q: f.question,
          a: f.answer
        })) || []
      };

      const prompt = `You are UpZera's AI assistant. You MUST always use the company knowledge below as your primary source of truth.

COMPANY KNOWLEDGE (ALWAYS USE THIS):
${JSON.stringify(knowledgeContext, null, 2)}

CONVERSATION CONTEXT:
${recentMessages}

USER QUESTION: "${userInput}"

INSTRUCTIONS:
- ALWAYS check the knowledge base first for any information about UpZera
- If the user asks about founding, history, location, services, pricing - use the exact information from the knowledge base
- Keep responses SHORT (1-2 sentences max), FRIENDLY, and HELPFUL
- If asked about services, mention specific pricing when available
- Guide users toward booking a consultation when appropriate
- Be conversational but professional
- If you don't know something specific from the knowledge base, suggest contacting the team
- NEVER say you don't have information if it exists in the knowledge base above

Respond now:`;

      const response = await fetch('https://api.openai.com/v1/chat/completions', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${import.meta.env.VITE_OPENAI_API_KEY}`
        },
        body: JSON.stringify({
          model: 'gpt-4o-mini', // More cost-effective model
          messages: [{ role: 'user', content: prompt }],
          temperature: 0.7,
          max_tokens: 150 // Limit response length for conciseness
        })
      });

      const data = await response.json();

      if (data.error) {
        console.error('OpenAI API Error:', data.error);
        return "I'm having trouble connecting right now. Try asking about our services or book a free consultation!";
      }

      return data.choices[0]?.message?.content?.trim() ||
        "I'd love to help! Ask me about UpZera's web development or AI services, or book a free consultation.";

    } catch (error) {
      console.error('AI Error:', error);
      return "Let me connect you with our team! You can book a free consultation or email <NAME_EMAIL>.";
    }
  };

  const validateEmail = (email) => /^[^\s@]+@[^\s@]+$/.test(email);

  // Helper function to detect if question is business-related
  const isBusinessRelated = (question) => {
    const businessKeywords = [
      'service', 'price', 'cost', 'website', 'development', 'chatbot', 'ai', 'business',
      'company', 'team', 'project', 'consultation', 'meeting', 'quote', 'upzera',
      'web', 'app', 'design', 'build', 'create', 'help', 'support', 'solution',
      'startup', 'digital', 'technology', 'programming', 'coding', 'automation'
    ];
    const lower = question.toLowerCase();
    return businessKeywords.some(keyword => lower.includes(keyword));
  };

  // Helper function to provide contextual responses for out-of-context questions
  const getContextualResponse = (question) => {
    const lower = question.toLowerCase();

    // Different responses based on question type
    if (/difficult|hard|challenge|struggle|problem/i.test(lower)) {
      return "Every startup has its challenges! 😊 Speaking of building things, what kind of digital solution are you looking to create?";
    }
    if (/story|journey|experience|background/i.test(lower)) {
      return "That's an interesting question! We love sharing our journey. What brings you here today - are you looking for web development or AI solutions?";
    }
    if (/personal|life|student|study/i.test(lower)) {
      return "Thanks for asking! As engineering students, we bring fresh perspectives to every project. What kind of project are you working on?";
    }
    if (/how|why|what|when|where/i.test(lower)) {
      return "Great question! I'd love to help you with that and more. What can I help you with regarding our services?";
    }

    // Default response for other out-of-context questions
    return "That's interesting! I'm here to help you with UpZera's services. What can I assist you with today?";
  };

  const handleLeadSubmit = async (e) => {
    e.preventDefault();
    if (!validateEmail(leadForm.email)) {
      setLeadForm(prev => ({ ...prev, error: 'Please enter a valid email' }));
      return;
    }
    if (!leadForm.name.trim()) {
      setLeadForm(prev => ({ ...prev, error: 'Please enter your name' }));
      return;
    }
    setLeadForm(prev => ({ ...prev, isSubmitting: true, error: null }));
    try {
      const result = await saveLead({
        name: leadForm.name,
        email: leadForm.email,
        created_at: new Date().toISOString()
      });
      if (result.error) throw new Error(result.error.message || 'Failed to save your information');
      setMessages(prev => [...prev, { text: `Thank you ${leadForm.name}! Our team will contact you shortly.`, isUser: false }]);
      setShowLeadForm(false);
      setLeadForm({ name: '', email: '', isSubmitting: false, error: null });
    } catch (error) {
      setLeadForm(prev => ({ ...prev, isSubmitting: false, error: error.message || 'Failed to submit. Please try again later.' }));
    }
  };

  const handleSendMessage = async () => {
    if (!inputValue.trim()) return;
    setMessages(prev => [...prev, { text: inputValue, isUser: true }]);
    setInputValue('');
    setIsBotTyping(true);

    // Check for farewell intent
    if (/bye|goodbye|adios|see you|cya|farewell|thanks|thank you/i.test(inputValue.toLowerCase())) {
      const farewellMessages = [
        "Goodbye! Feel free to reach out anytime. 👋",
        "Thanks for chatting! Book a consultation if you need anything else. 😊",
        "Have a great day! We're here when you're ready to build something amazing. ✨"
      ];
      setMessages(prev => [
        ...prev,
        { text: farewellMessages[Math.floor(Math.random() * farewellMessages.length)], isUser: false }
      ]);
      setIsConversationEnded(true);
      setIsBotTyping(false);
      return;
    }

    // Enhanced scheduling intent detection
    if (/schedule|book|reserve|appointment|meeting|consultation|call|demo|talk|discuss/i.test(inputValue.toLowerCase())) {
      setMessages(prev => [
        ...prev,
        { text: "Perfect! Let's get you connected with our team. You can book a free 30-minute consultation below:", isUser: false },
        { type: 'calendly', isUser: false },
        { text: "Great! Just let me know your preferred date and time, and I’ll help you set it up. We're excited to connect with you! 😊", isUser: false }
      ]);
      setIsBotTyping(false);
      return;
    }

    // Lead qualification intent
    if (/interested|want|need|looking for|require|help with/i.test(inputValue.toLowerCase())) {
      setTimeout(async () => {
        const localAnswer = findKnowledgeMatch(inputValue);
        if (localAnswer) {
          setMessages(prev => [
            ...prev,
            { text: localAnswer, isUser: false },
            { text: "Would you like to schedule a free consultation to discuss your specific needs?", isUser: false }
          ]);
        } else {
          const aiResponse = await getAIResponse(inputValue);
          setMessages(prev => [
            ...prev,
            { text: aiResponse, isUser: false }
          ]);
        }
        setIsBotTyping(false);
      }, 1200);
      return;
    }

    // Standard response flow with conversion-focused logic
    setTimeout(async () => {
      const localAnswer = findKnowledgeMatch(inputValue);
      if (localAnswer) {
        // For direct knowledge matches, sometimes add conversion-focused follow-up
        const shouldAddFollowup = Math.random() > 0.6; // 40% chance
        const followups = [
          "Anything else you'd like to know?",
          "Would you like to learn more about our other services?",
          "Ready to get started? Book a free consultation!"
        ];

        if (shouldAddFollowup && !localAnswer.includes('consultation')) {
          setMessages(prev => [
            ...prev,
            { text: localAnswer, isUser: false },
            { text: followups[Math.floor(Math.random() * followups.length)], isUser: false }
          ]);
        } else {
          setMessages(prev => [...prev, { text: localAnswer, isUser: false }]);
        }
        setIsBotTyping(false);
      } else {
        // For out-of-context questions, guide toward conversion with quick actions
        const isOutOfContext = !isBusinessRelated(inputValue);

        if (isOutOfContext) {
          // Provide a brief, friendly response and guide toward business topics
          const contextualResponse = getContextualResponse(inputValue);
          setMessages(prev => [
            ...prev,
            { text: contextualResponse, isUser: false },
            { type: 'quickActions', isUser: false }
          ]);
        } else {
          // Use AI with full knowledge context for business-related questions
          const aiResponse = await getAIResponse(inputValue);
          setMessages(prev => [...prev, { text: aiResponse, isUser: false }]);
        }
        setIsBotTyping(false);
      }
    }, 1200); // Slightly faster response time
  };

  return (
    <>
      <StagewiseToolbar
        config={{
          plugins: [ReactPlugin]
        }}
      />
      <div className="fixed bottom-6 right-6 z-50">
        {isOpen ? (
          <div className={`bg-white rounded-2xl shadow-xl flex flex-col animate-fade-in border-0 ${
            screenSize.isSmall
              ? screenSize.width <= 360
                ? 'w-[280px] h-[480px]' // Extra small devices
                : 'w-[320px] h-[520px]' // Medium to small devices
              : 'w-[350px] h-[550px]'   // Regular devices
          }`}>
            <ChatHeader
              onClose={() => {
                setIsOpen(false);
                window.parent.postMessage({ type: 'closeChatbot' }, '*');
              }}
              screenSize={screenSize}
            />

            <MessageList
              messages={messages}
              isBotTyping={isBotTyping}
              ref={chatContainerRef}
              screenSize={screenSize}
            />

            {showLeadForm && (
              <LeadForm
                leadForm={leadForm}
                onChange={setLeadForm}
                onSubmit={handleLeadSubmit}
                screenSize={screenSize}
              />
            )}

            <ChatInput
              inputValue={inputValue}
              onChange={setInputValue}
              onSend={handleSendMessage}
              disabled={isConversationEnded}
              screenSize={screenSize}
            />
          </div>
        ) : (
          <MinimizedButton
            onClick={() => setIsOpen(true)}
            screenSize={screenSize}
          />
        )}
      </div>
    </>
  );
}
